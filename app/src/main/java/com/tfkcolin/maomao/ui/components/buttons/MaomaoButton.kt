package com.tfkcolin.maomao.ui.components.buttons

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Button size variants
 */
enum class ButtonSize {
    Small, Medium, Large
}

/**
 * Primary button component following Material Design 3 guidelines
 * Used for main actions like "Add to Cart", "Buy Now", "Sign In"
 */
@Composable
fun PrimaryButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: ButtonSize = ButtonSize.Medium,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    fullWidth: Boolean = false
) {
    val buttonHeight = when (size) {
        ButtonSize.Small -> Size.buttonSmall
        ButtonSize.Medium -> Size.buttonMedium
        ButtonSize.Large -> Size.buttonLarge
    }
    
    val contentPadding = when (size) {
        ButtonSize.Small -> PaddingValues(horizontal = 12.dp, vertical = 6.dp)
        ButtonSize.Medium -> PaddingValues(horizontal = Spacing.buttonPadding, vertical = 8.dp)
        ButtonSize.Large -> PaddingValues(horizontal = 20.dp, vertical = 12.dp)
    }

    Button(
        onClick = onClick,
        modifier = modifier
            .height(buttonHeight)
            .then(if (fullWidth) Modifier.fillMaxWidth() else Modifier),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        contentPadding = contentPadding,
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.primary,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = MaterialTheme.colorScheme.outline,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        ButtonContent(
            text = text,
            isLoading = isLoading,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            size = size
        )
    }
}

/**
 * Secondary button component
 * Used for secondary actions like "Cancel", "Skip", "View Details"
 */
@Composable
fun SecondaryButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: ButtonSize = ButtonSize.Medium,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    fullWidth: Boolean = false
) {
    val buttonHeight = when (size) {
        ButtonSize.Small -> Size.buttonSmall
        ButtonSize.Medium -> Size.buttonMedium
        ButtonSize.Large -> Size.buttonLarge
    }
    
    val contentPadding = when (size) {
        ButtonSize.Small -> PaddingValues(horizontal = 12.dp, vertical = 6.dp)
        ButtonSize.Medium -> PaddingValues(horizontal = Spacing.buttonPadding, vertical = 8.dp)
        ButtonSize.Large -> PaddingValues(horizontal = 20.dp, vertical = 12.dp)
    }

    Button(
        onClick = onClick,
        modifier = modifier
            .height(buttonHeight)
            .then(if (fullWidth) Modifier.fillMaxWidth() else Modifier),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        contentPadding = contentPadding,
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.secondary,
            contentColor = MaterialTheme.colorScheme.onSecondary,
            disabledContainerColor = MaterialTheme.colorScheme.outline,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        ButtonContent(
            text = text,
            isLoading = isLoading,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            size = size
        )
    }
}

/**
 * Outlined button component
 * Used for tertiary actions or when you need a less prominent button
 */
@Composable
fun OutlinedMaomaoButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: ButtonSize = ButtonSize.Medium,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    fullWidth: Boolean = false
) {
    val buttonHeight = when (size) {
        ButtonSize.Small -> Size.buttonSmall
        ButtonSize.Medium -> Size.buttonMedium
        ButtonSize.Large -> Size.buttonLarge
    }
    
    val contentPadding = when (size) {
        ButtonSize.Small -> PaddingValues(horizontal = 12.dp, vertical = 6.dp)
        ButtonSize.Medium -> PaddingValues(horizontal = Spacing.buttonPadding, vertical = 8.dp)
        ButtonSize.Large -> PaddingValues(horizontal = 20.dp, vertical = 12.dp)
    }

    OutlinedButton(
        onClick = onClick,
        modifier = modifier
            .height(buttonHeight)
            .then(if (fullWidth) Modifier.fillMaxWidth() else Modifier),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        contentPadding = contentPadding,
        border = BorderStroke(
            width = 1.dp,
            color = if (enabled) MaterialTheme.colorScheme.outline else MaterialTheme.colorScheme.outline.copy(alpha = 0.38f)
        ),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = MaterialTheme.colorScheme.primary,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        ButtonContent(
            text = text,
            isLoading = isLoading,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            size = size
        )
    }
}

/**
 * Text button component
 * Used for the least prominent actions like "Learn More", "Forgot Password"
 */
@Composable
fun TextMaomaoButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: ButtonSize = ButtonSize.Medium,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null
) {
    val buttonHeight = when (size) {
        ButtonSize.Small -> Size.buttonSmall
        ButtonSize.Medium -> Size.buttonMedium
        ButtonSize.Large -> Size.buttonLarge
    }
    
    val contentPadding = when (size) {
        ButtonSize.Small -> PaddingValues(horizontal = 8.dp, vertical = 6.dp)
        ButtonSize.Medium -> PaddingValues(horizontal = 12.dp, vertical = 8.dp)
        ButtonSize.Large -> PaddingValues(horizontal = 16.dp, vertical = 12.dp)
    }

    TextButton(
        onClick = onClick,
        modifier = modifier.height(buttonHeight),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        contentPadding = contentPadding,
        colors = ButtonDefaults.textButtonColors(
            contentColor = MaterialTheme.colorScheme.primary,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        ButtonContent(
            text = text,
            isLoading = isLoading,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            size = size
        )
    }
}

/**
 * Internal composable for button content with loading state and icons
 */
@Composable
private fun ButtonContent(
    text: String,
    isLoading: Boolean,
    leadingIcon: ImageVector?,
    trailingIcon: ImageVector?,
    size: ButtonSize
) {
    val iconSize = when (size) {
        ButtonSize.Small -> Size.iconSmall
        ButtonSize.Medium -> Size.iconMedium
        ButtonSize.Large -> Size.iconLarge
    }
    
    val textStyle = when (size) {
        ButtonSize.Small -> MaterialTheme.typography.labelMedium
        ButtonSize.Medium -> MaterialTheme.typography.labelLarge
        ButtonSize.Large -> MaterialTheme.typography.titleMedium
    }

    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(iconSize),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.onPrimary
            )
            Spacer(modifier = Modifier.width(Spacing.small))
        } else {
            leadingIcon?.let { icon ->
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(iconSize)
                )
                Spacer(modifier = Modifier.width(Spacing.small))
            }
        }
        
        Text(
            text = text,
            style = textStyle.copy(fontWeight = FontWeight.Medium)
        )
        
        if (!isLoading) {
            trailingIcon?.let { icon ->
                Spacer(modifier = Modifier.width(Spacing.small))
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(iconSize)
                )
            }
        }
    }
}
