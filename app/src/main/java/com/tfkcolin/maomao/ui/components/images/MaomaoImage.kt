package com.tfkcolin.maomao.ui.components.images

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BrokenImage
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Enhanced image component with loading, error, and placeholder states
 */
@Composable
fun MaomaoImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(BorderRadius.medium),
    contentScale: ContentScale = ContentScale.Crop,
    placeholderIcon: ImageVector = Icons.Default.Image,
    errorIcon: ImageVector = Icons.Default.BrokenImage,
    showLoadingIndicator: Boolean = true,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    Box(
        modifier = modifier
            .background(backgroundColor, shape)
            .clip(shape),
        contentAlignment = Alignment.Center
    ) {
        if (imageUrl.isNullOrBlank()) {
            // Placeholder state
            Icon(
                imageVector = placeholderIcon,
                contentDescription = contentDescription,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(Size.iconLarge)
            )
        } else {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(imageUrl)
                    .crossfade(true)
                    .build(),
                contentDescription = contentDescription,
                contentScale = contentScale,
                modifier = Modifier.fillMaxSize(),
                onLoading = onLoading,
                onSuccess = onSuccess,
                onError = onError,
                placeholder = if (showLoadingIndicator) {
                    { LoadingPlaceholder() }
                } else null,
                error = {
                    ErrorPlaceholder(errorIcon = errorIcon)
                }
            )
        }
    }
}

/**
 * Product image component with specific aspect ratio and styling
 */
@Composable
fun ProductImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    aspectRatio: Float = 1f, // Square by default
    contentScale: ContentScale = ContentScale.Crop,
    showLoadingIndicator: Boolean = true,
    onImageClick: (() -> Unit)? = null
) {
    MaomaoImage(
        imageUrl = imageUrl,
        contentDescription = contentDescription,
        modifier = modifier
            .aspectRatio(aspectRatio)
            .then(
                if (onImageClick != null) {
                    Modifier.clickable { onImageClick() }
                } else Modifier
            ),
        shape = RoundedCornerShape(BorderRadius.medium),
        contentScale = contentScale,
        placeholderIcon = Icons.Default.Image,
        showLoadingIndicator = showLoadingIndicator
    )
}

/**
 * Avatar image component for user profiles
 */
@Composable
fun AvatarImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    size: Dp = Size.avatarMedium,
    contentScale: ContentScale = ContentScale.Crop,
    showLoadingIndicator: Boolean = true,
    backgroundColor: Color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
) {
    MaomaoImage(
        imageUrl = imageUrl,
        contentDescription = contentDescription,
        modifier = modifier.size(size),
        shape = CircleShape,
        contentScale = contentScale,
        placeholderIcon = Icons.Default.Person,
        showLoadingIndicator = showLoadingIndicator,
        backgroundColor = backgroundColor
    )
}

/**
 * Banner image component for promotions and headers
 */
@Composable
fun BannerImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    aspectRatio: Float = 16f / 9f, // 16:9 by default
    contentScale: ContentScale = ContentScale.Crop,
    showLoadingIndicator: Boolean = true,
    overlayContent: (@Composable () -> Unit)? = null
) {
    Box(
        modifier = modifier.aspectRatio(aspectRatio)
    ) {
        MaomaoImage(
            imageUrl = imageUrl,
            contentDescription = contentDescription,
            modifier = Modifier.fillMaxSize(),
            shape = RoundedCornerShape(BorderRadius.large),
            contentScale = contentScale,
            showLoadingIndicator = showLoadingIndicator
        )
        
        overlayContent?.let { content ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Color.Black.copy(alpha = 0.3f),
                        RoundedCornerShape(BorderRadius.large)
                    )
                    .padding(Spacing.medium),
                contentAlignment = Alignment.BottomStart
            ) {
                content()
            }
        }
    }
}

/**
 * Thumbnail image component for small previews
 */
@Composable
fun ThumbnailImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    size: Dp = Size.productImageSmall,
    contentScale: ContentScale = ContentScale.Crop,
    isSelected: Boolean = false,
    onThumbnailClick: (() -> Unit)? = null
) {
    val borderColor = if (isSelected) {
        MaterialTheme.colorScheme.primary
    } else {
        Color.Transparent
    }
    
    Box(
        modifier = modifier
            .size(size)
            .background(
                borderColor,
                RoundedCornerShape(BorderRadius.small + 2.dp)
            )
            .padding(if (isSelected) 2.dp else 0.dp)
    ) {
        MaomaoImage(
            imageUrl = imageUrl,
            contentDescription = contentDescription,
            modifier = Modifier
                .fillMaxSize()
                .then(
                    if (onThumbnailClick != null) {
                        Modifier.clickable { onThumbnailClick() }
                    } else Modifier
                ),
            shape = RoundedCornerShape(BorderRadius.small),
            contentScale = contentScale,
            showLoadingIndicator = false
        )
    }
}

/**
 * Loading placeholder component
 */
@Composable
private fun LoadingPlaceholder() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(Size.iconMedium),
            strokeWidth = 2.dp,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * Error placeholder component
 */
@Composable
private fun ErrorPlaceholder(
    errorIcon: ImageVector = Icons.Default.BrokenImage,
    message: String = "Failed to load image"
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(Spacing.small),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = errorIcon,
            contentDescription = "Error loading image",
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier.size(Size.iconLarge)
        )
        
        Text(
            text = message,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.error,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = Spacing.extraSmall)
        )
    }
}

/**
 * Image with shimmer loading effect
 */
@Composable
fun ShimmerImage(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(BorderRadius.medium)
) {
    Box(
        modifier = modifier
            .background(
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                shape
            )
            .clip(shape)
    ) {
        // Shimmer effect would be implemented here with animation
        // For now, showing a simple placeholder
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.6f)
                )
        )
    }
}
