# Maomao E-commerce UI Component System Documentation

## Overview

This document provides comprehensive documentation for the Maomao e-commerce Android app UI component system. The components are built using Jetpack Compose and follow Material Design 3 guidelines to create a professional, cohesive user experience.

## Design System Foundation

### Theme Configuration
- **Colors**: Extended color palette with primary, secondary, status, and e-commerce specific colors
- **Typography**: Consistent text styles following Material Design 3 typography scale
- **Spacing**: Systematic spacing tokens for consistent layouts
- **Elevation**: Standardized elevation levels for depth and hierarchy
- **Shapes**: Rounded corner system for consistent visual language

### Key Design Tokens
```kotlin
// Spacing
Spacing.small = 8.dp
Spacing.medium = 16.dp
Spacing.large = 24.dp

// Colors
PinduoduoRed = Color(0xFFE02E24)
PriceRed = Color(0xFFE53E3E)
DiscountGreen = Color(0xFF38A169)
GroupBuyPrimary = Color(0xFFFF6B35)

// Sizes
Size.buttonMedium = 40.dp
Size.iconMedium = 24.dp
Size.productImageMedium = 120.dp
```

## Component Categories

### 1. Button Components

#### PrimaryButton
Primary action button with consistent styling and loading states.

**Usage:**
```kotlin
PrimaryButton(
    text = "Add to Cart",
    onClick = { /* handle click */ },
    size = ButtonSize.Large,
    isLoading = false,
    leadingIcon = Icons.Default.ShoppingCart,
    fullWidth = true
)
```

**Props:**
- `text`: Button text
- `onClick`: Click handler
- `size`: ButtonSize (Small, Medium, Large)
- `isLoading`: Shows loading indicator
- `enabled`: Button enabled state
- `leadingIcon`/`trailingIcon`: Optional icons
- `fullWidth`: Expand to full width

#### E-commerce Specific Buttons
- `AddToCartButton`: Pre-configured for cart actions
- `BuyNowButton`: High-priority purchase button
- `JoinGroupBuyButton`: Group buy participation
- `WishlistButton`: Favorite toggle button

### 2. Input Components

#### MaomaoTextField
Enhanced text field with validation and error handling.

**Usage:**
```kotlin
MaomaoTextField(
    value = email,
    onValueChange = { email = it },
    label = "Email",
    variant = TextFieldVariant.Outlined,
    isError = emailError != null,
    errorMessage = emailError,
    keyboardType = KeyboardType.Email
)
```

**Specialized Text Fields:**
- `PasswordTextField`: With visibility toggle
- `EmailTextField`: Email-specific keyboard
- `PhoneTextField`: Phone number input
- `SearchTextField`: Search functionality
- `MultilineTextField`: For descriptions/reviews

#### Selection Components
- `DropdownSelector`: Dropdown selection
- `LabeledCheckbox`: Checkbox with label
- `LabeledRadioButton`: Radio button with label
- `RadioButtonGroup`: Group of radio buttons
- `CheckboxGroup`: Multi-select checkboxes

### 3. Card Components

#### MaomaoCard
Base card component with consistent styling.

**Usage:**
```kotlin
MaomaoCard(
    variant = CardVariant.Filled,
    onClick = { /* handle click */ }
) {
    // Card content
}
```

**Specialized Cards:**
- `ContentCard`: Title, description, and actions
- `InfoCard`: Icon, title, and description
- `SectionCard`: Grouped content with header
- `ExpandableCard`: Collapsible content

### 4. Image Components

#### MaomaoImage
Enhanced image component with loading, error, and placeholder states.

**Usage:**
```kotlin
MaomaoImage(
    imageUrl = product.imageUrl,
    contentDescription = product.name,
    shape = RoundedCornerShape(8.dp),
    showLoadingIndicator = true,
    onError = { /* handle error */ }
)
```

**Specialized Images:**
- `ProductImage`: Product-specific styling
- `AvatarImage`: Circular user avatars
- `BannerImage`: Promotional banners
- `ThumbnailImage`: Small previews

### 5. Product Components

#### EnhancedProductCard
Modern product card with comprehensive features.

**Usage:**
```kotlin
EnhancedProductCard(
    product = product,
    productVariant = variant,
    onProductClick = { productId -> /* navigate */ },
    onAddToCart = { /* add to cart */ },
    onToggleWishlist = { /* toggle favorite */ },
    groupBuy = groupBuy,
    rating = 4.5f,
    discountPercentage = 20,
    isOnSale = true
)
```

**Features:**
- Product images with badges
- Rating and review display
- Price with discounts
- Group buy progress
- Action buttons
- Wishlist functionality

#### ProductListing
Grid and list view layouts for product collections.

**Usage:**
```kotlin
ProductListing(
    products = productList,
    viewMode = ProductListingViewMode.Grid,
    onProductClick = { /* navigate */ },
    onAddToCart = { /* add to cart */ },
    onToggleWishlist = { /* toggle favorite */ },
    isLoading = false,
    title = "Featured Products"
)
```

### 6. Navigation Components

#### MaomaoBottomNavigation
Enhanced bottom navigation with badge support.

**Usage:**
```kotlin
MaomaoBottomNavigation(
    currentRoute = currentRoute,
    onNavigate = { route -> /* navigate */ },
    cartItemCount = 3,
    items = defaultBottomNavItems
)
```

#### MaomaoTopAppBar
Flexible top app bar with multiple variants.

**Usage:**
```kotlin
MaomaoTopAppBar(
    title = "Products",
    size = AppBarSize.Large,
    navigationIcon = Icons.Default.ArrowBack,
    onNavigationClick = { /* go back */ },
    actions = {
        IconButton(onClick = { /* search */ }) {
            Icon(Icons.Default.Search, "Search")
        }
    }
)
```

### 7. Loading Components

#### EnhancedLoadingIndicator
Customizable loading states.

**Usage:**
```kotlin
EnhancedLoadingIndicator(
    size = Size.iconLarge,
    message = "Loading products...",
    showMessage = true
)
```

**Loading States:**
- `FullScreenLoading`: Overlay loading
- `ProductCardSkeleton`: Shimmer placeholders
- `LoadingDots`: Animated dots
- `ProgressBarWithPercentage`: Progress tracking

### 8. Dialog Components

#### ConfirmationDialog
Confirmation dialogs with customizable actions.

**Usage:**
```kotlin
ConfirmationDialog(
    title = "Delete Item",
    message = "Are you sure you want to remove this item?",
    onConfirm = { /* delete */ },
    onDismiss = { /* cancel */ },
    type = DialogType.Warning,
    isDestructive = true
)
```

**Dialog Types:**
- `AlertMaomaoDialog`: Simple alerts
- `LoadingDialog`: Progress dialogs
- `SelectionDialog`: Option selection
- `InputDialog`: Text input

## Usage Guidelines

### 1. Consistency
- Use design tokens for spacing, colors, and sizes
- Follow established patterns for similar components
- Maintain consistent interaction patterns

### 2. Accessibility
- Provide meaningful content descriptions
- Ensure minimum touch target sizes (48dp)
- Support screen readers and accessibility services
- Use appropriate color contrast ratios

### 3. Performance
- Use lazy loading for large lists
- Implement proper state management
- Optimize image loading with placeholders
- Use skeleton loaders for better perceived performance

### 4. Responsive Design
- Components adapt to different screen sizes
- Use flexible layouts with proper constraints
- Consider landscape and portrait orientations

## Examples

### Complete Product Listing Screen
```kotlin
@Composable
fun ProductListingScreen() {
    Column {
        MaomaoTopAppBar(
            title = "Products",
            size = AppBarSize.Large,
            actions = {
                IconButton(onClick = { /* search */ }) {
                    Icon(Icons.Default.Search, "Search")
                }
            }
        )
        
        ProductListing(
            products = productList,
            viewMode = viewMode,
            onProductClick = { productId -> /* navigate */ },
            onAddToCart = { product -> /* add to cart */ },
            onToggleWishlist = { product -> /* toggle favorite */ },
            isLoading = isLoading,
            title = "Featured Products",
            onFilterClick = { /* show filters */ },
            onSortClick = { /* show sort options */ }
        )
    }
}
```

### Product Detail Actions
```kotlin
@Composable
fun ProductActions() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        AddToCartButton(
            onClick = { /* add to cart */ },
            modifier = Modifier.weight(1f),
            isLoading = isAddingToCart
        )
        
        WishlistButton(
            isFavorited = isFavorited,
            onToggle = { /* toggle wishlist */ }
        )
    }
}
```

## Best Practices

1. **State Management**: Use StateFlow for reactive state updates
2. **Error Handling**: Provide clear error messages and recovery options
3. **Loading States**: Show appropriate loading indicators
4. **Validation**: Implement real-time form validation
5. **Navigation**: Use consistent navigation patterns
6. **Testing**: Write comprehensive tests for components
7. **Documentation**: Keep component documentation up to date

## Component Testing

Each component should include:
- Unit tests for logic
- UI tests for interactions
- Accessibility tests
- Performance tests for complex components

## Future Enhancements

- Dark mode support improvements
- Animation enhancements
- Advanced filtering components
- Voice search integration
- AR product preview components
- Social sharing components
